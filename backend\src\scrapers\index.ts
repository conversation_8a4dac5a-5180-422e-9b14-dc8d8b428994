import { BeikeScraper } from './beike';

import { NanjingAnjukeScraper } from './anjuke-nanjing';

export class ScraperManager {
  private beikeScraper: BeikeScraper;

  private nanjingAnjukeScraper: NanjingAnjukeScraper;

  constructor() {
    this.beikeScraper = new BeikeScraper();

    this.nanjingAnjukeScraper = new NanjingAnjukeScraper();
  }

  async runAllScrapers(maxPages: number = 3): Promise<void> {
    console.log('Starting scraper job...');
    
    try {
      // 爬取贝壳网数据
      console.log('Scraping Beike...');
      const beikeListings = await this.beikeScraper.scrapeMultiplePages(maxPages);
      await this.beikeScraper.saveListings(beikeListings);
      console.log(`Saved ${beikeListings.length} Beike listings`);

      // 延迟一段时间再爬取安居客
      await this.delay(5000);

      // 爬取安居客数据
      console.log('Scraping Anjuke...');
      // 爬取所有区域
      const anjukeListings = await this.nanjingAnjukeScraper.scrapeAllAreas(undefined, maxPages);
      await this.nanjingAnjukeScraper.saveListings(anjukeListings);
      console.log(`Saved ${anjukeListings.length} Anjuke listings`);

      console.log('Scraper job completed successfully');
    } catch (error) {
      console.error('Error in scraper job:', error);
      throw error;
    }
  }

  async runBeikeOnly(maxPages: number = 3): Promise<void> {
    console.log('Starting Beike scraper...');
    try {
      const listings = await this.beikeScraper.scrapeMultiplePages(maxPages);
      await this.beikeScraper.saveListings(listings);
      console.log(`Beike scraper completed. Saved ${listings.length} listings`);
    } catch (error) {
      console.error('Error in Beike scraper:', error);
      throw error;
    }
  }

  async runAnjukeOnly(maxPages: number = 3): Promise<void> {
    console.log('Starting Anjuke scraper...');
    try {
      const listings = await this.nanjingAnjukeScraper.scrapeAllAreas('', maxPages);
      await this.nanjingAnjukeScraper.saveListings(listings);
      console.log(`Anjuke scraper completed. Saved ${listings.length} listings`);
    } catch (error) {
      console.error('Error in Anjuke scraper:', error);
      throw error;
    }
  }

  async runNanjingAnjukeOnly(areaFilter?: string, maxPages: number = 3): Promise<void> {
    console.log('Starting Nanjing Anjuke scraper...');
    try {
      const listings = await this.nanjingAnjukeScraper.scrapeAllAreas(areaFilter, maxPages);
      await this.nanjingAnjukeScraper.saveListings(listings);
      console.log(`Nanjing Anjuke scraper completed. Saved ${listings.length} listings`);
    } catch (error) {
      console.error('Error in Nanjing Anjuke scraper:', error);
      throw error;
    }
  }

  async runNanjingAnjukeByArea(areaName: string, areaFilter?: string, maxPages: number = 3): Promise<void> {
    console.log(`Starting Nanjing Anjuke scraper for area: ${areaName}...`);
    try {
      const listings = await this.nanjingAnjukeScraper.scrapeAreaPages(areaName, areaFilter, maxPages);
      await this.nanjingAnjukeScraper.saveListings(listings);
      console.log(`Nanjing Anjuke scraper completed for ${areaName}. Saved ${listings.length} listings`);
    } catch (error) {
      console.error(`Error in Nanjing Anjuke scraper for ${areaName}:`, error);
      throw error;
    }
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

export { BeikeScraper, NanjingAnjukeScraper };
