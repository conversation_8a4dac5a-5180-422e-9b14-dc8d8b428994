import * as cheerio from 'cheerio';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface NanjingAnjukeListingData {
  listingId: string;
  title: string;
  link: string;
  community: string;
  district: string;
  totalPrice: number;
  unitPrice: number;
  area: number;
  layout: string;
  floor?: string;
  orientation?: string;
  buildYear?: number;
  imageUrl?: string;
  tags?: string[];
  hasVR?: boolean;
}

export class NanjingAnjukeScraper {
  private baseUrl = 'https://nanjing.anjuke.com/sale';

  // 区域代码映射
  private areaCodes = {
    '百家湖': 'bjhnj',
    '岔路口': 'chaluko',
    '东山': 'njdongshan',
    '将军大道': 'jiangjundadao',
    '九龙湖': 'njkaifaqu',
    '科学园': 'kexueyuan'
  };

  // 面积筛选映射
  private areaFilters = {
    '100-110平方': 'a16346',
    '110-130平方': 'a16347'
  };

  private userAgents = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  ];

  private getRandomUserAgent(): string {
    return this.userAgents[Math.floor(Math.random() * this.userAgents.length)];
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private getRandomDelay(): number {
    const min = parseInt(process.env.SCRAPER_DELAY_MIN || '1000');
    const max = parseInt(process.env.SCRAPER_DELAY_MAX || '3000');
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  /**
   * 构建搜索URL
   * @param areaName 区域名称
   * @param areaFilter 面积筛选（可选）
   * @param page 页码
   */
  private buildUrl(areaName: string, areaFilter?: string, page: number = 1): string {
    const areaCode = this.areaCodes[areaName as keyof typeof this.areaCodes];
    if (!areaCode) {
      throw new Error(`Unsupported area: ${areaName}`);
    }

    // 构建URL: https://nanjing.anjuke.com/sale/jiangninga-q-${areacode}/${area}-p${pageNum}/
    let url = `${this.baseUrl}/jiangninga-q-${areaCode}/`;

    // 添加面积筛选
    if (areaFilter) {
      const filterCode = this.areaFilters[areaFilter as keyof typeof this.areaFilters];
      if (filterCode) {
        url += `${filterCode}-`;
      }
    }

    // 添加页码
    url += `p${page}/`;

    return url;
  }

  async scrapePage(areaName: string, areaFilter?: string, page: number = 1): Promise<NanjingAnjukeListingData[]> {
    try {
      const url = this.buildUrl(areaName, areaFilter, page);
      console.log(`Scraping Nanjing Anjuke page: ${url}`);

      // 添加延迟模拟真实用户行为
      await this.delay(1000 + Math.random() * 2000);

      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Accept-Encoding': 'gzip, deflate, br',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
          'Sec-Fetch-Dest': 'document',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-Site': 'none',
          'Sec-Fetch-User': '?1',
          'Cache-Control': 'max-age=0',
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const html = await response.text();

      // 检查是否遇到反爬虫验证页面
      if (html.includes('@@xxzlGatewayUrl') || html.includes('verifycode') || html.length < 1000) {
        console.warn(`Detected anti-bot verification page for ${areaName} page ${page}`);
        console.warn('HTML length:', html.length);

        // 尝试提取验证URL并警告
        const $ = cheerio.load(html);
        const verifyUrl = $('#@@xxzlGatewayUrl').text();
        if (verifyUrl) {
          console.warn('Verification URL detected:', verifyUrl);
        }

        return [];
      }

      const $ = cheerio.load(html);
      const listings: NanjingAnjukeListingData[] = [];

      $('.property').each((_, element) => {
        try {
          const $item = $(element);
          
          // 提取房源链接和ID
          const link = $item.find('a').attr('href') || '';
          const listingIdMatch = link.match(/\/(\d+)/);
          if (!listingIdMatch) return;
          
          const listingId = `anjuke_nj_${listingIdMatch[1]}`;
          const fullLink = link.startsWith('http') ? link : `https://nanjing.anjuke.com${link}`;
          
          // 提取标题
          const title = $item.find('.property-content-title-name').text().trim();
          if (!title) return;

          // 检查是否有VR看房
          const hasVR = $item.find('.vr').length > 0;

          // 提取价格信息
          const totalPriceText = $item.find('.property-price-total-num').text().trim();
          const totalPrice = parseFloat(totalPriceText) || 0;
          
          const unitPriceText = $item.find('.property-price-average').text().replace(/[^\d]/g, '');
          const unitPrice = parseFloat(unitPriceText) || 0;

          // 提取房屋基本信息
          const propertyInfo = $item.find('.property-content-info');
          
          // 户型信息 (3室2厅2卫)
          const roomCount = propertyInfo.find('.property-content-info-attribute span').eq(0).text().trim();
          const hallCount = propertyInfo.find('.property-content-info-attribute span').eq(2).text().trim();
          const bathCount = propertyInfo.find('.property-content-info-attribute span').eq(4).text().trim();
          const layout = `${roomCount}室${hallCount}厅${bathCount}卫`;

          // 面积
          const areaText = propertyInfo.find('.property-content-info-text').eq(1).text().trim();
          const area = parseFloat(areaText.replace(/[^\d.]/g, '')) || 0;

          // 朝向
          const orientation = propertyInfo.find('.property-content-info-text').eq(2).text().trim();

          // 楼层信息
          const floor = propertyInfo.find('.property-content-info-text').eq(3).text().trim();

          // 建造年份
          const buildYearText = propertyInfo.find('.property-content-info-text').eq(4).text().trim();
          const buildYearMatch = buildYearText.match(/(\d{4})/);
          const buildYear = buildYearMatch ? parseInt(buildYearMatch[1]) : undefined;

          // 小区和地址信息
          const community = $item.find('.property-content-info-comm-name').text().trim();
          const addressParts = $item.find('.property-content-info-comm-address span');
          const district = addressParts.eq(0).text().trim();

          // 提取标签
          const tags: string[] = [];
          $item.find('.property-content-info-tag').each((_, tagEl) => {
            const tag = $(tagEl).text().trim();
            if (tag) tags.push(tag);
          });

          // 提取图片
          const imageUrl = $item.find('.property-image img').attr('src') || 
                          $item.find('.property-image img').attr('data-src') || '';

          if (title && totalPrice > 0 && area > 0) {
            listings.push({
              listingId,
              title,
              link: fullLink,
              community,
              district,
              totalPrice,
              unitPrice,
              area,
              layout,
              floor,
              orientation,
              buildYear,
              imageUrl,
              tags,
              hasVR
            });
          }
        } catch (error) {
          console.error('Error parsing Nanjing Anjuke listing item:', error);
        }
      });

      console.log(`Scraped ${listings.length} listings from Nanjing Anjuke page ${page} (${areaName})`);
      return listings;
    } catch (error) {
      console.error(`Error scraping Nanjing Anjuke page ${page} (${areaName}):`, error);
      return [];
    }
  }

  /**
   * 爬取指定区域的多个页面
   * @param areaName 区域名称
   * @param areaFilter 面积筛选（可选）
   * @param maxPages 最大页数
   */
  async scrapeAreaPages(areaName: string, areaFilter?: string, maxPages: number = 3): Promise<NanjingAnjukeListingData[]> {
    const allListings: NanjingAnjukeListingData[] = [];

    for (let page = 1; page <= maxPages; page++) {
      const listings = await this.scrapePage(areaName, areaFilter, page);
      allListings.push(...listings);

      // 如果当前页没有数据，说明已经到最后一页
      if (listings.length === 0) {
        console.log(`No more listings found for ${areaName}, stopping at page ${page}`);
        break;
      }

      // 随机延迟避免被封
      if (page < maxPages) {
        await this.delay(this.getRandomDelay());
      }
    }

    return allListings;
  }

  /**
   * 爬取所有支持的区域
   * @param areaFilter 面积筛选（可选）
   * @param maxPages 每个区域的最大页数
   */
  async scrapeAllAreas(areaFilter?: string, maxPages: number = 3): Promise<NanjingAnjukeListingData[]> {
    const allListings: NanjingAnjukeListingData[] = [];
    const areaNames = Object.keys(this.areaCodes);

    for (const areaName of areaNames) {
      console.log(`Starting to scrape area: ${areaName}`);
      try {
        const areaListings = await this.scrapeAreaPages(areaName, areaFilter, maxPages);
        allListings.push(...areaListings);
        console.log(`Completed scraping ${areaName}: ${areaListings.length} listings`);

        // 区域间延迟
        if (areaName !== areaNames[areaNames.length - 1]) {
          await this.delay(this.getRandomDelay() * 2);
        }
      } catch (error) {
        console.error(`Error scraping area ${areaName}:`, error);
      }
    }

    return allListings;
  }

  /**
   * 保存房源数据到数据库
   * @param listings 房源数据列表
   */
  async saveListings(listings: NanjingAnjukeListingData[]): Promise<void> {
    for (const listing of listings) {
      try {
        await prisma.housingListing.upsert({
          where: { listingId: listing.listingId },
          update: {
            title: listing.title,
            link: listing.link,
            community: listing.community,
            district: listing.district,
            totalPrice: listing.totalPrice,
            unitPrice: listing.unitPrice,
            area: listing.area,
            layout: listing.layout,
            floor: listing.floor,
            orientation: listing.orientation,
            buildYear: listing.buildYear,
            imageUrl: listing.imageUrl,
            lastScrapedAt: new Date(),
            isActive: true
          },
          create: {
            listingId: listing.listingId,
            source: 'anjuke_nanjing',
            title: listing.title,
            link: listing.link,
            community: listing.community,
            district: listing.district,
            totalPrice: listing.totalPrice,
            unitPrice: listing.unitPrice,
            area: listing.area,
            layout: listing.layout,
            floor: listing.floor,
            orientation: listing.orientation,
            buildYear: listing.buildYear,
            imageUrl: listing.imageUrl,
            isActive: true
          }
        });

        // 记录价格历史
        const existingListing = await prisma.housingListing.findUnique({
          where: { listingId: listing.listingId },
          include: { priceHistory: { orderBy: { scrapedAt: 'desc' }, take: 1 } }
        });

        if (existingListing && existingListing.priceHistory.length > 0) {
          const lastPrice = existingListing.priceHistory[0].price;
          if (Math.abs(lastPrice - listing.totalPrice) > 0.01) {
            await prisma.priceHistory.create({
              data: {
                listingId: existingListing.id,
                price: listing.totalPrice
              }
            });
          }
        } else if (existingListing) {
          await prisma.priceHistory.create({
            data: {
              listingId: existingListing.id,
              price: listing.totalPrice
            }
          });
        }
      } catch (error) {
        console.error(`Error saving Nanjing Anjuke listing ${listing.listingId}:`, error);
      }
    }
  }

  /**
   * 获取支持的区域列表
   */
  getSupportedAreas(): string[] {
    return Object.keys(this.areaCodes);
  }

  /**
   * 获取支持的面积筛选列表
   */
  getSupportedAreaFilters(): string[] {
    return Object.keys(this.areaFilters);
  }
}
